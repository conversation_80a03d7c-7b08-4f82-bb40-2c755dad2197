{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Xcv30f9KD+C2jm/2o+0RYqNv72bLjts9lsEy01BMyp4=", "7TGRzwMZZPj9spV6CHG1dZmtmhfDZXGCd/Tzc8sCR2o=", "EBpgSypzq+zwvbEL6CHoYPJ4K5xhEXuSRTwnihDYuT8=", "GLnf/cq0jI7jHSiTA5/cD6ljS7hdjbyOPIBnqN7EdCo=", "K/Ri+aKMTwU/Gp5GqOKWCaejEJFN5xRgg9qDqyG8CXk=", "n8Be5KOoSnc1oducgrz1EcVmCsYvkl8DRDODchl4vjo=", "0vokHwj7JaygfRhfzP8DO5Ni/V5Rvu+hRG08ZviktkQ=", "YRo3eFaw4J+zOXHT6CI0TYSSXONN6A7zgCsaODqGuXU=", "cXeKVJbYrhQJ/VzBLOOjvbe588Ym+zzTgjbXGJTE7fY=", "+uYsSBttK3W2dUTOq5oJgFv01kUzSRzU1jjpy6iCCHE=", "pAgNjnjkMhO+/glNTiX6hqC0eEjd2IEyvADqvdErbYQ=", "+8fVXzEwZzaJ437vxgFLZCkY+7H5Nf/UfQhKr0bDxm0=", "kv5u0IVMS7cMR8WXQhUa2xB5MlMErUiOOrY3oPYQG5w=", "GTMFl3Wj0lhcx1P9lUXRd2nDc77gO8tJww4xC4KPpoI=", "kjy5m3fzzuNs9YgXKpLsUFdwkm1LE6EVoJOm/9922lI=", "FTUQTsmASOKhdmllY4QqEcn3w8brh+D0tssVRuzrGvA=", "Wx7dgsfGFrLIplipMis8u4ZWzvzNQNEkCu9wijj2Ntw=", "wxD7MU4ldUpZikxrumSXpXT4x8Jm9ni+BRyxgaIVcIc=", "du2YMFc+kqqKhhcRF5MNupkiG5MhKCzs0qdM18nl04k=", "59YLM5a4n2/hZPR6W+fki06D3DfmlQ1hTSE1Cx32JUQ=", "Bv1JtLPcz02PSQ+Pjw2oLPqki1wdnfkrDQvhHqgXdZo=", "8nLKeyCWAd5j/wWG2zzhVVt7WN/a3IOgB9FqwowCqnw=", "C4KMwzRX8Pr8YOgiad8qHtjU729pqP0vbZfK7LvaGNo=", "NKGLhEvRp+80GZxP/oK7Yu4DBxz968JfasTZZ5Hv1Ts=", "65OO0x4MpXX8EbLWq4DVqowoXKoyGxYPiKUhFDzxxOE=", "te2OYTitn8TGUdPXGSSXEJcrx+RR9Tf4W7Xd8O57wiU=", "B4FPk7G4A0TdFapfhYiQHLLrIYDd+sOHuugoRbntrck=", "mf13LU5LDXyah9M5egX2Mx4krg3bmFG7H+3BuZfUuYM=", "IW1+o0u74dBgRz/EwzDUhi71HRX/Ir+bm3KjQ6HmqHw=", "ccLhsRpFTQ514B/R7QWNOkGP5NWdAPZOFotaBS4QWGY=", "jYpD6IL+c2XTgTgZBJ/qNQmqD0Z/FglIaFPKbKs628w=", "opuoDDCqPenXaj/i+pm+wr/4y68UAITc64gB15XvnLs=", "fFZUtf9CBcNfziVDtKF8Qrv1456RKGsAZZQVunVN5AM=", "9BezWHLXZT8Oj5lDcbpuSukal7UHi51Y+mfoW6EnmWc=", "lwqQzugyPBnKmYKFSswNue63haJlQm6MWLCroo+k3D0=", "pkZnbMlOMdt1acqRcL0D06rFbaq7cVeZNRxceLl5Kfc=", "wftemIRGd6lP3vXqjGJERAM1bCC61vazXX7YuMp+AoY=", "ER2RqRU68yvTqIG3X84zMugpnG/0e6z1OCfqE1JiBlw=", "WTds4nVz5922U/x040a6sZb5WtLon1D+DMKS0iwb/XQ=", "TuYnBxJsWzuRGY1CUd+rKc/7CNNXPynm40Qvntojg3M=", "S0xGu2uVlKup3z0DVEwgbnCpelduLLpbx+21DJa87P0=", "7llxf1bLNSfZl/gUFDHD0sFF4eXaibtayMEkk73npoc=", "DXubAptsZtL+QMsQRlvMqTTuv62LZLwDapOyF0goaoI=", "C4hPLdAsww4O9gnvrUPk03dQKMAMUdtTvN634mL61cY=", "RaQTVi812UG0Hsph5MAE5hK6I4mXWaDB3liIKzERmBI=", "DhqM5vl94CSuV9pZNeuHoT7sftowJY4ypFGLqwyQK6A=", "qjklXsaeBN9cIEvXOwLfIMSBVxF1tmDhMAX+P+8XQBI=", "pd9s9On/9sjMQjIbeKhoDNf6Xj2Mq5rmRLQjNdfFYAM=", "J0vDUPdpbkEqoOWaZh4Xr+6cUWc3KS1EkkzloEjtJ5c=", "xw2flBrb1B9lJblEVAmaWy7fi3ETBOi8kl4SeTcWBco=", "NpqmTz4jnGUzK6i2J2OCTS8yjFhtdq/xKhTH0EPYnfU=", "Q3tcNy61PhSMa3zgwShl712PrFsK8Yj2QFlmPuK9Km0=", "xUUxuhysXHbZb4NrYMm+XMQvb9zbbuC28gDfaybhR4U=", "TEvHfTWa1UoNV5HkSwk4mbbndI6SMLw1c7letSYPcYI=", "tvbiPU5V13ZInp4YPmkf0unbNyCcTJGi+Q/o6fWfW/E=", "mBwNhnEGRyjTH/S4bfLtqTW+wu1Lecvf//wvm2CMTyI=", "1KXPh0hLSTTO6ccGMnWk+CicMZJXFq5VO4KBMlHATfU=", "zmOIAPdeEySuoyZOHY5gseL+DMuaiC+B9uM8idgbC0E=", "gjBm9dc746aq2YfMUfxuSmc2Rt8ZASYi8qSTVZAna1k=", "MAJ/5WV4aKE1IxZX+OEvsDFHWwwtR6K0Jwkc1e3kBXg=", "ltRBAwjuPG22cqVYMsl+d9Pmx9JTY4XVKtbfJ/rr1yk=", "/hEYQxDRbAnDhAtvctgEFk/rsXYiUClvAxydvH+Ekn0=", "TSG6PeHVZhTzkTGHw3WMrXeDlPkwnzdWGZ2dLDL+CvE=", "3xAKWKMx6PXNdj8/cbGZjlVtZYf7ko+fCeoQc3G9y/E=", "FhymvTdjhbCz7W/J9k3BHPN44TRKHSUiOBrprVGnmKc=", "ZVNdwkyS14PcH8++ehAn8vlVorJRlyIMu3U5bCaJRRA=", "1NU70FiISbLdbWQ5rtTtWzTujw97S5QJmKZ5p0zdtZo=", "Eav56Kgqh0J9PhIz/2Tm4dKjnMHP8Nj/a/dJMNLNUfk=", "qUiBdpYN+qsXqinmwoIKAAmmP4z6rzGNa00AKtTJu/A=", "waOcYIizswhj4biDamkgRhpprwbahNOeeYTHBm0KPKE=", "PFOvn+v6Add8J+A81UItAfIa3+bBLqLQI1zOyJpn+YE=", "Em6fMB/K3GY4RoJidQnjQZuK+8Bg7QWUbW5d5QHYuck=", "9QUHK6T7tt7bkPs01MG/RTftY7X1GQUx60PZRXx3Tks=", "FQgJcJkquoJ0EzSYT0XrcYwFq14T7FpUIZqejHSHqaQ=", "w3eYMLpSLZ09oQX/SkkFrsGE9819X4K+NaCZFAnY+Wk=", "GkAkWQLxAgBCtH0FpqzGsjVGVQIe4tdzoNj9Nyf3Qf8=", "Mr5OEnAbsXvpDhuCfcVB4aTNxZOMEcv+Ry78FY5pnV0=", "E8drHQcvBC2CB/kuD+VRoqPUpfXiwKfM7xdiHxHdaA4=", "c0e4gFvF7ZfwQBJlZc1X1WDUgqgc+ws6MusFzPqZzoo=", "aGPURQBWU6d1cXh0KdXJvX0lRnZecx1vumEAo94DM0k=", "dwtjkmCFjjbNZYtj0Gmqrv6xrxEsMRztjOkBVvnF7wQ=", "7IjfUCMC3cooTVspT4EUik6J5/r1Ucj7OkhOL5TzKpo=", "YNVN7isCqSPOIPxIi50VXQe+5SwQzbN5novEq4BR7o4=", "0ZqH97S41N5lI41tmarpmA+3lerce0+fMfpCBwneovg=", "R2z5cqzBkmo8qOVRXp3bEOhZx2KCqKrn0ormotikyHA=", "YSzJSb9fCxjEhQiuo6vyPT0vNyAy/4jhzVzmpYIcqNo=", "17gyk89Pwi0NZIzqDZIBPUlpV5s+oE0v3gK3PRtdPho=", "2VZWVcZX6h3gKr8ltkh8XQQcwsD1t+pqsAZlQMfGKfE=", "Sy3RV+hw7C1UpCcPK44QebGi4Ab1ZDm+8Jg7K42C840=", "oGL4vOCi4TWwhV3BJGQm49ZpiNkmHoQHQFsCOoXrG1s=", "ceDKxZV2Z8SaYNj5DEoDOEdWV6N+ICPueEqusqjzEJk=", "jQVkfHUfZSpF3RowgsTvDAakzoC/+uGFlA85BMM6mqI=", "FFETInoRyENct9vQ90olcf8ye9G1XuA65kfqy96iRVY=", "yy/mVyS7lcj7cSc+w6pBWGlDFoyssxxj9EDZkTlQZ/k=", "TismDtG6O6gqPquGzdgSR+o73eTXjczhOsn4Gojskw0=", "39FodxNWl55Zl0ID/hcvFrJJYncLdeHLAleAyZtjIF0=", "UPG+N+DdB0wx9OugEclNbJ3v40SoFBFqOo7p9UzdBzY=", "yoOFC7Z1AGYQjxMTQxBeml/24bj90keRRuPpK6AkLS8=", "7Qh4HufPDvHXzI9PYDE9kEB5NsHxqEpxiNMWfTTuw9I=", "ksen72jfmuP8VpUKcNy1BmrSgGhEQcPaOqxqlRzLKR0=", "hNc7jdOj/Qp3alTtEj+AzSTkM8ZQeTjBqk8gVYEJlaM=", "dnbTqV+iJz9B37ytiUTzCaLFzMmcX7B+pdoL6Uj0Ow0=", "pe5KbqpBldMGARVXGKyG09IyKybqBkYM+kn/buQl0Q4=", "wRaX3SFF1hxIU3aSlcYJXDGN11+ByuZ+938pmiPImmE=", "bASdDAdDVhXqaeuxHaSieSb/GNK4/Uf4C/RBbrIZ2Eg=", "VZxByXkH8kmwcpLAjpj3RopaLo1HsIhFJISra8IGyb4=", "WRm/OFEzZu7rJ6wgX6Mn44P5pc5iWhn2lZWKAU9KVeM=", "qT+shzgWkgbcoLWD9UNghREchQRAyY7YYrskmD2bkyE=", "yoswkaQOWN9rs4iqukcqDvtiMiIUGJnnQ6n5rSp+rHc=", "gnaKhZBqHZjkPWvHAS6ri2Mkk8xxQ0ITLE8JoeWoFcc=", "qwzJ90aG/8FIHCZONN3pbLziGPFIVMD7KCx/kyQgYpk=", "QHbSPhyoNbQU3erQiEAcM2mJod9967MmQE6a30qAKrA=", "TCfsOgPdAJ+ZSvVSpUw/856c/TmRCIcCx6XeSlJOxZw=", "HlKeHVUpiAUEzpE53YWodH/rWMCk2cFl7xHoXh6vWSk=", "quKrq8wKpVwoB5r4uqT8/eXDutYkUsTN4CzH9tpEACs=", "mDZrfjhba9WhUxs1yd3JHASZRkVauycsOeyW/UKKekk=", "jMwWW96WR4hIrX1MIWCi5z1HC2ixwB2SRmi3mkaLxw0=", "U7o1RWZ4pYuJGyNqJq7jAEyLJXwT1k5ouMvjAaa8N64=", "mc2V2b19QRyi6jyiS0mEVIDtSsaGH4Fs6fA8P2cajtI=", "dEJ4YIP8kLJTlGFgmOd3urN8cJA35Pta4/BiraxCm5Y=", "b5O1ya6gxSPMgIcXLJxclVEcLmwCxZ6E2LtUdNSeDWk=", "rrAnPCRCTwNjfkaLj0usULzq3WFeS9R+BYPeerhUwCM=", "grWX+mra8bskmw5kwt3z5d+fkGW9ocYT6WKujusoQ0U=", "IwQUUbq+iZNNbPXw53duxalreTCXpXNTN5+mX5o8VXo=", "wN71+r6LYQe+2EmSHX/GY+KqE8QNBJXmiXDPOwLeoYQ=", "ytKUcJ9tz9k4DuCSOEqJQQQVo5AYgiyViZInL6nPpps=", "nE+XhHwbbmSzInfh+ghK4oXn4Zrb7kYVLwqCiP8VegA=", "+HZrfOPYiVkSWPKZapMo61dKfst10AgdRwf/2AIO448=", "xYVi6melf9k4kvNR9775wY2V2STXjnRjWrXOje5hFyA=", "2d8y2qG4ifEwdLYKDtYG1rb5ZS5e1WqxlnEDCjRPi2Q=", "8CYjuYNlgBkQ9FgkT29ZYIpOzk9Nc3BWyznodcoSORM=", "OWAAQYJo5NUrgowY2lzV+JWdc9E2H9iFs01rRu6WyqA=", "jBIq2Cd7PrK/wsYIhZ6bMcq4fN/hX65IgpYdluFgj2s=", "h7A6QcJKS7BJ5MqDJMVeP4dj+Jm31wrDM8y3N5WyNzU=", "dJ1vXs2SnK01OsP1pJMvRPX08ZBi7Onp7o/btFsPT2Q=", "c2cWmojav/bn/xX3MDj6obiPC6OYrwvkH4MjjbFmzH4=", "AZfmiQwSygRWs3viREHWzPbDKxiQOhaPZyYjPR06UGE=", "Jrb3yLpcaJ+/FjP605dYUXwk4+TiYuZaLk9PR8JkvOM=", "OyGiSz/4R+kxt6+NoFzJPS/mLJZiMdX9LYz1Te7g8ZM=", "JRzLmmK6RBB5UPMTGXaEFdndIEprmSffhu+LfDexehE=", "JdmSvN1AsU8RYW7QHIXwp5uBjfSl+X6NTtlTN7CiRvQ=", "Lgl6Mg255yKsQ3WLXWLFkSO6hUO3BLTUojGJE0DIxlg=", "S+Dddc/vAamnyvB2mLnHSzM5/lMcSAXD2nFCPu34EGs=", "tgZzSLflQ6UWCxAhQOHF+8NoW6xu5deINzIZrabpxeQ=", "nVgVF6HxQdE+/blrbviGdM0QjAo76KCp1JwWLEJWVrs=", "BgmFdvOxN7MhTlYq6bSmcbvtt5qKJXT3aopS7cNrq1c=", "Da165OcVHHgTaDQMKgSMLQeGw2glsZx833qVxNEV/Qo=", "fGNPXiTfUw4iFUb9osTg3j+HBwUHcJSrK6qCeodXO+E=", "mJN2kXXamyCI+QmyYPTlqqf0EVc7j5kR6HVSNt7FiEo=", "LSopD/yPfRWodo2cPJnWVpYhVHtLdLEWeJR/Hvs2vBc=", "DaOIMR69QA6VRoQudT6pN3vXEa+c83ikqqdNYo56mbo=", "ZgmLbP0QmQ6KHnCqJTGx10v6x+av5TTz60H6+cI+c88=", "28L7wmX/XJwGuUa3PPc2C23oi2fViLpEYx4ZxpkVjYM=", "fXqrrr9LoiFqBMqikKqDbUC75QBBCiwFAH2aY3w1aYc=", "1PTo8YPEreQS8Quu7XhFmQsXWBHKGXqy3yDS4VzL3ao=", "TssZW1xHVsSZAvS9lSxE/ITXlLdqMg5XAu6EB0jGfSI=", "+J7qTBmnBhgt70nsbwmgYSBeUwElIiJgzWrelNqB0x4=", "9tefALxJBgN/UJ1BcipA7R++UwGUPip8cKU83TiOAXE=", "54rWRA3FO5foEjm9C1dQgB2Q7N1zdWWOKIdUdP9zDhY=", "bodQ2X5qaqUhOScklExU1jLXcO6Vmi6sQcsPd6wdvmk="], "CachedAssets": {"Xcv30f9KD+C2jm/2o+0RYqNv72bLjts9lsEy01BMyp4=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wf4wcyqcqt", "Integrity": "fNKkLSNFLZMVTc2zVrpMXQqOwPU3sU4GWcfEbCR2V1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\css\\site.css", "FileLength": 319, "LastWriteTime": "2025-05-20T02:18:00.3013742+00:00"}, "7TGRzwMZZPj9spV6CHG1dZmtmhfDZXGCd/Tzc8sCR2o=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xngppdbdhq", "Integrity": "nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\favicon.ico", "FileLength": 9431, "LastWriteTime": "2025-05-20T02:18:00.1732036+00:00"}, "EBpgSypzq+zwvbEL6CHoYPJ4K5xhEXuSRTwnihDYuT8=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fpxp8wntf7", "Integrity": "465WqepNnI4ENLgQJFXrL2gtI20GCfeLK3JAtugOzpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\js\\site.js", "FileLength": 190, "LastWriteTime": "2025-05-20T02:18:00.4547073+00:00"}, "GLnf/cq0jI7jHSiTA5/cD6ljS7hdjbyOPIBnqN7EdCo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1uwaguxw6", "Integrity": "HQp0c6N8q5wOxo27/7+5nSrp8SBfCepJdWIMB3pb5bE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6915, "LastWriteTime": "2025-05-20T02:17:59.7704333+00:00"}, "K/Ri+aKMTwU/Gp5GqOKWCaejEJFN5xRgg9qDqyG8CXk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-05-20T02:18:00.3666142+00:00"}, "n8Be5KOoSnc1oducgrz1EcVmCsYvkl8DRDODchl4vjo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s32t06avo3", "Integrity": "6OfodiC592J173EssQMUTEfylPvm7++5l9PRY+7T5Uo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6102, "LastWriteTime": "2025-05-20T02:18:00.0787221+00:00"}, "0vokHwj7JaygfRhfzP8DO5Ni/V5Rvu+hRG08ZviktkQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-05-20T02:17:59.9415149+00:00"}, "YRo3eFaw4J+zOXHT6CI0TYSSXONN6A7zgCsaODqGuXU=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ql842l5", "Integrity": "+UkMsfq0zPH03cQtfw9papLT2sHleN3bw7S1s5iLonY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6918, "LastWriteTime": "2025-05-20T02:18:00.1307243+00:00"}, "cXeKVJbYrhQJ/VzBLOOjvbe588Ym+zzTgjbXGJTE7fY=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-05-20T02:18:00.0047104+00:00"}, "+uYsSBttK3W2dUTOq5oJgFv01kUzSRzU1jjpy6iCCHE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ykjynei6kc", "Integrity": "Xll87KRp2km7kw+kDSFiBiyUQnZ7QCVIb8yJtRDXckQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6104, "LastWriteTime": "2025-05-20T02:18:00.0817141+00:00"}, "pAgNjnjkMhO+/glNTiX6hqC0eEjd2IEyvADqvdErbYQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-05-20T02:18:00.3987894+00:00"}, "+8fVXzEwZzaJ437vxgFLZCkY+7H5Nf/UfQhKr0bDxm0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "syqesifl59", "Integrity": "/Cti7KtgK7n0FoWSWRMvmhTwGSxieEUp+Ao+6Xxnmug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3375, "LastWriteTime": "2025-05-20T02:17:59.8281115+00:00"}, "kv5u0IVMS7cMR8WXQhUa2xB5MlMErUiOOrY3oPYQG5w=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-05-20T02:18:00.3181789+00:00"}, "GTMFl3Wj0lhcx1P9lUXRd2nDc77gO8tJww4xC4KPpoI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fx04t62wt", "Integrity": "z14pI3bmpzGTIVaJu6aKOcry1zSAu/H44LHt9J3bXFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3199, "LastWriteTime": "2025-05-20T02:18:00.1862635+00:00"}, "kjy5m3fzzuNs9YgXKpLsUFdwkm1LE6EVoJOm/9922lI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-05-20T02:17:59.9187711+00:00"}, "FTUQTsmASOKhdmllY4QqEcn3w8brh+D0tssVRuzrGvA=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vkbr9nomgm", "Integrity": "oSfmOJQjIULAN8mPr84YlRct/JVoFbWAn9Y7IWzBuoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3364, "LastWriteTime": "2025-05-20T02:18:00.0617334+00:00"}, "Wx7dgsfGFrLIplipMis8u4ZWzvzNQNEkCu9wijj2Ntw=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-05-20T02:18:00.4284343+00:00"}, "wxD7MU4ldUpZikxrumSXpXT4x8Jm9ni+BRyxgaIVcIc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b8ltiz8u9h", "Integrity": "u85nx859JKxhSe8TTSe97CuakJmuU36EC4JdxkCZL3g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3231, "LastWriteTime": "2025-05-20T02:18:00.4173852+00:00"}, "du2YMFc+kqqKhhcRF5MNupkiG5MhKCzs0qdM18nl04k=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-05-20T02:18:00.2847443+00:00"}, "59YLM5a4n2/hZPR6W+fki06D3DfmlQ1hTSE1Cx32JUQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3lpifp44y", "Integrity": "9EqjQR8ugEerwGk0t0elN3RQ+XHT25kPo1+/yYXwvNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12127, "LastWriteTime": "2025-05-20T02:17:59.7814029+00:00"}, "Bv1JtLPcz02PSQ+Pjw2oLPqki1wdnfkrDQvhHqgXdZo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-05-20T02:17:59.9376666+00:00"}, "8nLKeyCWAd5j/wWG2zzhVVt7WN/a3IOgB9FqwowCqnw=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cy4j794sg8", "Integrity": "LPVzscuhkQC3vZzaHKyyJLBxtZ0JDszgPmlT3zj1wGg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11149, "LastWriteTime": "2025-05-20T02:18:00.4587036+00:00"}, "C4KMwzRX8Pr8YOgiad8qHtjU729pqP0vbZfK7LvaGNo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-05-20T02:18:00.057613+00:00"}, "NKGLhEvRp+80GZxP/oK7Yu4DBxz968JfasTZZ5Hv1Ts=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "letd63iq5t", "Integrity": "49exWGtFTeHrxPgcV2SALyeXqbJ0Gx2BAW7ghwTbDHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12069, "LastWriteTime": "2025-05-20T02:17:59.8946678+00:00"}, "65OO0x4MpXX8EbLWq4DVqowoXKoyGxYPiKUhFDzxxOE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-05-20T02:18:00.1217247+00:00"}, "te2OYTitn8TGUdPXGSSXEJcrx+RR9Tf4W7Xd8O57wiU=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ukjvmro89", "Integrity": "QDcr93RhSeRIcvSG2UO4XpXn7SmN9PibCcICeplm1tk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11127, "LastWriteTime": "2025-05-20T02:18:00.2977832+00:00"}, "B4FPk7G4A0TdFapfhYiQHLLrIYDd+sOHuugoRbntrck=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-05-20T02:18:00.1537323+00:00"}, "mf13LU5LDXyah9M5egX2Mx4krg3bmFG7H+3BuZfUuYM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp6dicxiq4", "Integrity": "MfXRRxefGD0S0k5f3gXwZNQW7ELiStY01dZj+IbLlaw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33478, "LastWriteTime": "2025-05-20T02:18:00.2938424+00:00"}, "IW1+o0u74dBgRz/EwzDUhi71HRX/Ir+bm3KjQ6HmqHw=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-05-20T02:17:59.8371251+00:00"}, "ccLhsRpFTQ514B/R7QWNOkGP5NWdAPZOFotaBS4QWGY=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uhnhmx07wd", "Integrity": "zrRIXAC8ugCIlsRMgBBjTa8xli0BiiAqT375rZab79Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31118, "LastWriteTime": "2025-05-20T02:17:59.8539501+00:00"}, "jYpD6IL+c2XTgTgZBJ/qNQmqD0Z/FglIaFPKbKs628w=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-05-20T02:17:59.9926092+00:00"}, "opuoDDCqPenXaj/i+pm+wr/4y68UAITc64gB15XvnLs=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7kvveri0mt", "Integrity": "h+uVSG7EhchzF96LorZkFbGFBO8oQowufr5PhxsGq8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33366, "LastWriteTime": "2025-05-20T02:18:00.2052565+00:00"}, "fFZUtf9CBcNfziVDtKF8Qrv1456RKGsAZZQVunVN5AM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-05-20T02:18:00.3817142+00:00"}, "9BezWHLXZT8Oj5lDcbpuSukal7UHi51Y+mfoW6EnmWc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3m4n8jcl3h", "Integrity": "5VQx+pcREAWRC7fSQ1OVsJtpTGF7SJ83RS8p2rovHHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31137, "LastWriteTime": "2025-05-20T02:18:00.5279172+00:00"}, "lwqQzugyPBnKmYKFSswNue63haJlQm6MWLCroo+k3D0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-05-20T02:18:00.3535157+00:00"}, "pkZnbMlOMdt1acqRcL0D06rFbaq7cVeZNRxceLl5Kfc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqu1wssclk", "Integrity": "Fd462fjZQtrrvL/sHbIIZF7ktZgtHombUabahz+Nhoo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44461, "LastWriteTime": "2025-05-20T02:17:59.7530728+00:00"}, "wftemIRGd6lP3vXqjGJERAM1bCC61vazXX7YuMp+AoY=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iwjfk8z8sl", "Integrity": "fgl+pethjpTJQrRxrFinuKNP9hP56Cpi71nXoWFk1oE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92277, "LastWriteTime": "2025-05-20T02:18:00.4937052+00:00"}, "ER2RqRU68yvTqIG3X84zMugpnG/0e6z1OCfqE1JiBlw=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18qy0lzppu", "Integrity": "QZ0MSDfNrGl5pbF5ppDikD9YeYfOenEETYBSBemyReU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23799, "LastWriteTime": "2025-05-20T02:18:00.2273024+00:00"}, "WTds4nVz5922U/x040a6sZb5WtLon1D+DMKS0iwb/XQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jjusqppj5", "Integrity": "YNK7tH4KLEhmt17m2X41sl74ZCDUilGpyhRBJKwG0P4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86518, "LastWriteTime": "2025-05-20T02:18:00.1507249+00:00"}, "TuYnBxJsWzuRGY1CUd+rKc/7CNNXPynm40Qvntojg3M=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z27fl73l7", "Integrity": "K+JV1bWtCSH6pUbk4PUJUao1Gc0EagazN8O9VnhFaPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28886, "LastWriteTime": "2025-05-20T02:18:00.1347243+00:00"}, "S0xGu2uVlKup3z0DVEwgbnCpelduLLpbx+21DJa87P0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6e2hoibiq5", "Integrity": "oglZ6aWY/UXq+QsVhwhOwfIZbgvz7Dvjg/GfXmQsBVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64234, "LastWriteTime": "2025-05-20T02:18:00.3858061+00:00"}, "7llxf1bLNSfZl/gUFDHD0sFF4eXaibtayMEkk73npoc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6t36crcw3z", "Integrity": "Ng/NTAPb2HvIa++U+TsQnZ6UYlXnzyFuh5Df7vHJZgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18542, "LastWriteTime": "2025-05-20T02:18:00.4707082+00:00"}, "DXubAptsZtL+QMsQRlvMqTTuv62LZLwDapOyF0goaoI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03mhy7u5rs", "Integrity": "9Hc7N6ug4kO+ZAzS1pFxw7Hbp+FZaCKMnDLRAv1J/zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56385, "LastWriteTime": "2025-05-20T02:18:00.5199167+00:00"}, "C4hPLdAsww4O9gnvrUPk03dQKMAMUdtTvN634mL61cY=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "winy7sfnv8", "Integrity": "mLLuMjFYOIYX8xSc8UD/TLCKJOjlhcscgrxC5va1aAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29657, "LastWriteTime": "2025-05-20T02:18:00.5600887+00:00"}, "RaQTVi812UG0Hsph5MAE5hK6I4mXWaDB3liIKzERmBI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fizolni560", "Integrity": "OeWySIbjCJXM4ZgVwiGmsjpLeFsT+qOpONsDqh8xk5M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64490, "LastWriteTime": "2025-05-20T02:18:00.1427249+00:00"}, "DhqM5vl94CSuV9pZNeuHoT7sftowJY4ypFGLqwyQK6A=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnp63td8x7", "Integrity": "YQ6ZOC5+9dlRHEgMrLJKheWx2C2Xk7AL5Hy36EOfqq8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16571, "LastWriteTime": "2025-05-20T02:18:00.5309178+00:00"}, "qjklXsaeBN9cIEvXOwLfIMSBVxF1tmDhMAX+P+8XQBI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s9vzkbm4vd", "Integrity": "EImWkSMXEOh40HFlhmq90VTXlOCYqWVzWn4vPxUhiA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55524, "LastWriteTime": "2025-05-20T02:18:00.3260761+00:00"}, "pd9s9On/9sjMQjIbeKhoDNf6Xj2Mq5rmRLQjNdfFYAM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj59c5yssu", "Integrity": "TWRCXMwzQLwwVw7TcG+7g24PsqDnarIGBgITeMiCp10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4663, "LastWriteTime": "2025-05-20T02:18:00.1613184+00:00"}, "J0vDUPdpbkEqoOWaZh4Xr+6cUWc3KS1EkkzloEjtJ5c=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tabrq1ho0f", "Integrity": "9X+6mMpWl0sRImYav09zbU/QCCt+GeH7cDib6C/z2lI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2202, "LastWriteTime": "2025-05-20T02:18:00.3414727+00:00"}, "xw2flBrb1B9lJblEVAmaWy7fi3ETBOi8kl4SeTcWBco=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vtotz30el", "Integrity": "1HkjQSVuV0/ji/X/lGeDTHeagBS0BwopMrHDQuwqgtU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 680, "LastWriteTime": "2025-05-20T02:18:00.2221604+00:00"}, "NpqmTz4jnGUzK6i2J2OCTS8yjFhtdq/xKhTH0EPYnfU=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvtge0zj2y", "Integrity": "u0Z1XIrPIXG0QZK+EQOG85U9O7JhC+GVwFW+Rcyvbyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 13955, "LastWriteTime": "2025-05-20T02:18:00.5230226+00:00"}, "Q3tcNy61PhSMa3zgwShl712PrFsK8Yj2QFlmPuK9Km0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ka6j29rtmm", "Integrity": "nJwi+an4Wr4A7wVYax2DH+rKvwNbT+YQBBWTeP8GLr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6382, "LastWriteTime": "2025-05-20T02:18:00.4097136+00:00"}, "xUUxuhysXHbZb4NrYMm+XMQvb9zbbuC28gDfaybhR4U=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w44s95dr06", "Integrity": "8HCMA1neUf8Vrpg7qfQgzCVX6n/hFKgcFl5RKQuVC1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14023, "LastWriteTime": "2025-05-20T02:18:00.4363222+00:00"}, "TEvHfTWa1UoNV5HkSwk4mbbndI6SMLw1c7letSYPcYI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m7rg4chp1z", "Integrity": "Po4sWHCLIbaqYqyEYnIYtBO+dRjwxtbreif7YdpGQxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8053, "LastWriteTime": "2025-05-20T02:17:59.8024197+00:00"}, "tvbiPU5V13ZInp4YPmkf0unbNyCcTJGi+Q/o6fWfW/E=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbedkqp182", "Integrity": "yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 670, "LastWriteTime": "2025-05-20T02:17:59.9484132+00:00"}, "mBwNhnEGRyjTH/S4bfLtqTW+wu1Lecvf//wvm2CMTyI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v75tr20pas", "Integrity": "q0KM+9yOLRYeTH/iGBE/HUYO08aXn2DmZ+yyIU6/kho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84055, "LastWriteTime": "2025-05-20T02:18:00.4214343+00:00"}, "1KXPh0hLSTTO6ccGMnWk+CicMZJXFq5VO4KBMlHATfU=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lbv9l0z68", "Integrity": "bSlV8nIWgnEsf0ljiCRK0xw364UXDcqZZilq1CQ+At0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30413, "LastWriteTime": "2025-05-20T02:18:00.4058132+00:00"}, "zmOIAPdeEySuoyZOHY5gseL+DMuaiC+B9uM8idgbC0E=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sdwt97mj8r", "Integrity": "0HH7P7Mtrs+2sBMSFXD8DJw0oV4iDB5wkeRFpSi113A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 53929, "LastWriteTime": "2025-05-20T02:17:59.9784693+00:00"}, "gjBm9dc746aq2YfMUfxuSmc2Rt8ZASYi8qSTVZAna1k=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yow8h3bl0n", "Integrity": "fhspAbITSYOUuRd6s5oYdr7V1DC3ibPbW5Siv7MGps8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68315, "LastWriteTime": "2025-05-20T02:17:59.9609768+00:00"}, "MAJ/5WV4aKE1IxZX+OEvsDFHWwwtR6K0Jwkc1e3kBXg=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gm2ug2dj20", "Integrity": "OK9mKWiOoFmOZ+xYop9MRSKE6LHMu/9ZTCPvluUOpcA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24089, "LastWriteTime": "2025-05-20T02:18:00.2777406+00:00"}, "ltRBAwjuPG22cqVYMsl+d9Pmx9JTY4XVKtbfJ/rr1yk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uiwq7himce", "Integrity": "2u2XFgZyC96dP/elrO5ny/maD/llVl8JsX3pBm2SWT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 42735, "LastWriteTime": "2025-05-20T02:18:00.4436051+00:00"}, "/hEYQxDRbAnDhAtvctgEFk/rsXYiUClvAxydvH+Ekn0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdl0higpfn", "Integrity": "wRqs30CSrwWDvG1bDMUjomAlSqRRTMplLSuLcrgYtHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.2\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 669, "LastWriteTime": "2025-05-20T02:18:00.5121139+00:00"}, "TSG6PeHVZhTzkTGHw3WMrXeDlPkwnzdWGZ2dLDL+CvE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ve0wfylib0-zgp2gsnx4w.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/admin#[.{fingerprint=zgp2gsnx4w}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\admin.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5gtm20pkeb", "Integrity": "lRATkJTaVNxXpT+UtVGQlZucWCtxrdzJLLKQZdYa+L8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\admin.css", "FileLength": 1482, "LastWriteTime": "2025-05-20T02:21:15.8261489+00:00"}, "3xAKWKMx6PXNdj8/cbGZjlVtZYf7ko+fCeoQc3G9y/E=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\3t88wunzhd-gsbku0aiqh.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/ai-loading#[.{fingerprint=gsbku0aiqh}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\ai-loading.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1658s9ugtf", "Integrity": "KQw0f3+llWFieMw4cPsANGZ4wEoEf77ZOh/GdyRGTTU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\ai-loading.css", "FileLength": 911, "LastWriteTime": "2025-05-20T02:21:15.8248389+00:00"}, "FhymvTdjhbCz7W/J9k3BHPN44TRKHSUiOBrprVGnmKc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\bwhfu9eppd-k0homqapq4.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/animations#[.{fingerprint=k0homqapq4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\animations.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2b5f27sfbc", "Integrity": "ngbfLgcHmBsJl8z9K3aayjLa+CDkNmzkKDn7ZUMq2lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\animations.css", "FileLength": 1346, "LastWriteTime": "2025-05-20T02:21:15.8261489+00:00"}, "1NU70FiISbLdbWQ5rtTtWzTujw97S5QJmKZ5p0zdtZo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\81k276nu5s-qhlzhi182j.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/pannellum#[.{fingerprint=qhlzhi182j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\pannellum.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1a7tm59r4z", "Integrity": "59vZA0DKV5LrN3pUjn6VDaEMZ8UOI3UoINgQ3Ltavaw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\pannellum.css", "FileLength": 175, "LastWriteTime": "2025-05-20T02:21:15.8376684+00:00"}, "Eav56Kgqh0J9PhIz/2Tm4dKjnMHP8Nj/a/dJMNLNUfk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\c2mnxoilje-8ly5wdcvf9.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/panorama360#[.{fingerprint=8ly5wdcvf9}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\panorama360.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7bp7c<PERSON>u4", "Integrity": "HWzw1mZSpx2vZXEc+awOVdO3SGj6FsgKhGI3IIiCty8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\panorama360.css", "FileLength": 561, "LastWriteTime": "2025-05-20T02:21:15.8547316+00:00"}, "1PTo8YPEreQS8Quu7XhFmQsXWBHKGXqy3yDS4VzL3ao=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\hkhyi1hpxp-6p85mnpqpi.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/destination-showcase#[.{fingerprint=6p85mnpqpi}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\destination-showcase.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "higkq4axxj", "Integrity": "mVotzgVBX8EglgPjj6EVNwssHY5dwyss4HB8AfnkjPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\destination-showcase.css", "FileLength": 3890, "LastWriteTime": "2025-05-24T15:26:08.4013413+00:00"}, "9tefALxJBgN/UJ1BcipA7R++UwGUPip8cKU83TiOAXE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\l6754aewc2-315jyb5umc.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/unified-style#[.{fingerprint=315jyb5umc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\unified-style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "suu3of1mzh", "Integrity": "9AI5CpOUxEVt+riUbF9Jv1M0aXr4cbpcmAyrLInMDdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\unified-style.css", "FileLength": 1324, "LastWriteTime": "2025-05-24T15:26:08.4063644+00:00"}, "PFOvn+v6Add8J+A81UItAfIa3+bBLqLQI1zOyJpn+YE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\wzvjqq03zi-61n19gt1b8.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-05-20T02:21:15.8904056+00:00"}, "Em6fMB/K3GY4RoJidQnjQZuK+8Bg7QWUbW5d5QHYuck=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\hgt0foqgh4-3beyq0z36m.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "images/banners/README#[.{fingerprint=3beyq0z36m}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\images\\banners\\README.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gm6irdjv95", "Integrity": "CDFgKKAtMR9OCDSMSwk6sVpN4e7Y1MwKVHbRlcXYGaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\images\\banners\\README.txt", "FileLength": 91, "LastWriteTime": "2025-05-20T02:21:15.8238363+00:00"}, "9QUHK6T7tt7bkPs01MG/RTftY7X1GQUx60PZRXx3Tks=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\gkh4qz1j5u-a40m3gly00.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "images/banners/README_AI#[.{fingerprint=a40m3gly00}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\images\\banners\\README_AI.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tegdzk8e5k", "Integrity": "q5h7gDa9nsP7NRTGVt6RG/qoXI7Ni6IBT7SwqEraIhY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\images\\banners\\README_AI.txt", "FileLength": 250, "LastWriteTime": "2025-05-20T02:21:15.8311691+00:00"}, "w3eYMLpSLZ09oQX/SkkFrsGE9819X4K+NaCZFAnY+Wk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\8jqfn2bsel-rt921ccr5e.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "images/destinations/README#[.{fingerprint=rt921ccr5e}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\images\\destinations\\README.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iznate2b8s", "Integrity": "7qwG3MDWIC9SxGWt8sTbmtj8BVM2QWykoWFhVhfSGGU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\images\\destinations\\README.html", "FileLength": 1362, "LastWriteTime": "2025-05-20T02:21:15.8439192+00:00"}, "E8drHQcvBC2CB/kuD+VRoqPUpfXiwKfM7xdiHxHdaA4=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\jm6xeu8kep-b19b82u1kz.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "images/tours/README#[.{fingerprint=b19b82u1kz}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\images\\tours\\README.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dcnc0x4xe3", "Integrity": "LaU0JjXsOPg7BgDNAo9HO1PXVJipTF+4V1Galdf0zKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\images\\tours\\README.txt", "FileLength": 171, "LastWriteTime": "2025-05-20T02:21:15.8601824+00:00"}, "c0e4gFvF7ZfwQBJlZc1X1WDUgqgc+ws6MusFzPqZzoo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\3n8buq3yyp-b0lvkkndk6.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/ai-loading#[.{fingerprint=b0lvkkndk6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\ai-loading.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2s3osbolwq", "Integrity": "3m6v2KhLv2fyAwEhPzQUtAx1wMDj2YZPq2jIoPHY9II=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\ai-loading.js", "FileLength": 1093, "LastWriteTime": "2025-05-20T02:21:15.8709025+00:00"}, "dwtjkmCFjjbNZYtj0Gmqrv6xrxEsMRztjOkBVvnF7wQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\0wxs44p2r0-bkcfsouzr3.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/disable-price-validation#[.{fingerprint=bkcfsouzr3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\disable-price-validation.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kln5r6n8du", "Integrity": "SLY2iP0nxNqgvuYeHwy/CfptXzgeWKHp43zZCotmJhk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\disable-price-validation.js", "FileLength": 566, "LastWriteTime": "2025-05-20T02:21:15.8790618+00:00"}, "7IjfUCMC3cooTVspT4EUik6J5/r1Ucj7OkhOL5TzKpo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\2wctqttgfo-u521jm4b18.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/googleMapsConfig#[.{fingerprint=u521jm4b18}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\googleMapsConfig.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "911g6yznbs", "Integrity": "PedQHz+LGew6t8317Q00Jplb5ngtMJ00osZvcSyofXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\googleMapsConfig.js", "FileLength": 772, "LastWriteTime": "2025-05-20T02:21:15.8850585+00:00"}, "0ZqH97S41N5lI41tmarpmA+3lerce0+fMfpCBwneovg=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ziuc7xkdjq-izse9ukono.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/pannellum.min#[.{fingerprint=izse9ukono}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\pannellum.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lsutzoz2h1", "Integrity": "26vpbhIAkdyUovz4Tvt3e3wr3qsL99jbeceI49L0QAo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\pannellum.min.js", "FileLength": 174, "LastWriteTime": "2025-05-20T02:21:15.8301637+00:00"}, "R2z5cqzBkmo8qOVRXp3bEOhZx2KCqKrn0ormotikyHA=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\3rr46j87l7-8mqg9v5qlv.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/panolens.min#[.{fingerprint=8mqg9v5qlv}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\panolens.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qy0irk0wh1", "Integrity": "YqBiMwE9bERofT5vqo683OFasynNICMkoEKrYBGqaUc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\panolens.min.js", "FileLength": 184, "LastWriteTime": "2025-05-20T02:21:15.8301637+00:00"}, "YSzJSb9fCxjEhQiuo6vyPT0vNyAy/4jhzVzmpYIcqNo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\gseoxyatag-u1xa0i0140.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/recommendation#[.{fingerprint=u1xa0i0140}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\recommendation.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yr2bfr6jfz", "Integrity": "MkL0SjrjoyfwxpUqAa37n+bJU77EpokVAvI4WyjbWnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\recommendation.js", "FileLength": 1358, "LastWriteTime": "2025-05-20T02:21:15.8389026+00:00"}, "jQVkfHUfZSpF3RowgsTvDAakzoC/+uGFlA85BMM6mqI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\1t41zakfyi-tch3nzi1fh.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/site#[.{fingerprint=tch3nzi1fh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j41gbqpvf4", "Integrity": "s0wrj9RYrBo3hdpyILJ32vDDLL0ALgFrLSOy6J9vDg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\site.js", "FileLength": 2194, "LastWriteTime": "2025-05-20T02:21:15.8547316+00:00"}, "FFETInoRyENct9vQ90olcf8ye9G1XuA65kfqy96iRVY=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\xm3c7nmbdm-iv241s19du.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/three.min#[.{fingerprint=iv241s19du}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\three.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9s1e1i7jie", "Integrity": "iVl1wq81XUtsRACSHgDPI6V4RCf55/0H5r0YeMeQoqM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\three.min.js", "FileLength": 174, "LastWriteTime": "2025-05-20T02:21:15.8656214+00:00"}, "yy/mVyS7lcj7cSc+w6pBWGlDFoyssxxj9EDZkTlQZ/k=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\lc35lsjltw-t1cqhe9u97.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7trewrvtze", "Integrity": "td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6754, "LastWriteTime": "2025-05-20T02:21:15.8770672+00:00"}, "TismDtG6O6gqPquGzdgSR+o73eTXjczhOsn4Gojskw0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\s5mdz1l4m5-c2jlpeoesf.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-20T02:21:15.8894082+00:00"}, "39FodxNWl55Zl0ID/hcvFrJJYncLdeHLAleAyZtjIF0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\6ru9r5xvw4-sejl45xvog.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhey2fgxm6", "Integrity": "2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5973, "LastWriteTime": "2025-05-20T02:21:15.832165+00:00"}, "UPG+N+DdB0wx9OugEclNbJ3v40SoFBFqOo7p9UzdBzY=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\cqy89g8dll-aexeepp0ev.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-20T02:21:15.8459172+00:00"}, "yoOFC7Z1AGYQjxMTQxBeml/24bj90keRRuPpK6AkLS8=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\zpcwoqkbpz-xvp3kq03qx.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xcrflemqm", "Integrity": "Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6755, "LastWriteTime": "2025-05-20T02:21:15.8656214+00:00"}, "7Qh4HufPDvHXzI9PYDE9kEB5NsHxqEpxiNMWfTTuw9I=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\vx8fuqr0rf-ausgxo2sd3.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-20T02:21:15.8810581+00:00"}, "ksen72jfmuP8VpUKcNy1BmrSgGhEQcPaOqxqlRzLKR0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\rp6i2jmmao-22vffe00uq.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxqr4d2em4", "Integrity": "Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5976, "LastWriteTime": "2025-05-20T02:21:15.8934073+00:00"}, "hNc7jdOj/Qp3alTtEj+AzSTkM8ZQeTjBqk8gVYEJlaM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\4m6dnxd0iq-cosvhxvwiu.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-20T02:21:15.9048096+00:00"}, "dnbTqV+iJz9B37ytiUTzCaLFzMmcX7B+pdoL6Uj0Ow0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\orfexza8l6-qesaa3a1fm.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dtwnk525ov", "Integrity": "H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3407, "LastWriteTime": "2025-05-20T02:21:15.9129748+00:00"}, "pe5KbqpBldMGARVXGKyG09IyKybqBkYM+kn/buQl0Q4=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\0yatax766c-fvhpjtyr6v.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-20T02:21:15.8366657+00:00"}, "wRaX3SFF1hxIU3aSlcYJXDGN11+ByuZ+938pmiPImmE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\26nq1ji911-tmc1g35s3z.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7jljgo6o9r", "Integrity": "w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3217, "LastWriteTime": "2025-05-20T02:21:15.8471462+00:00"}, "bASdDAdDVhXqaeuxHaSieSb/GNK4/Uf4C/RBbrIZ2Eg=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\djqcuenosm-fsbi9cje9m.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-20T02:21:15.8641827+00:00"}, "VZxByXkH8kmwcpLAjpj3RopaLo1HsIhFJISra8IGyb4=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\y3y9q1yld8-rxsg74s51o.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmcjt9e9cy", "Integrity": "ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3397, "LastWriteTime": "2025-05-20T02:21:15.8728968+00:00"}, "WRm/OFEzZu7rJ6wgX6Mn44P5pc5iWhn2lZWKAU9KVeM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\v8609ljy81-ee0r1s7dh0.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-20T02:21:15.8840714+00:00"}, "qT+shzgWkgbcoLWD9UNghREchQRAyY7YYrskmD2bkyE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\j64halhzcl-q9ht133ko3.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fijz6kmv0", "Integrity": "AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3250, "LastWriteTime": "2025-05-20T02:21:15.8914094+00:00"}, "yoswkaQOWN9rs4iqukcqDvtiMiIUGJnnQ6n5rSp+rHc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\6u686x4ohk-jd9uben2k1.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-20T02:21:15.8995579+00:00"}, "gnaKhZBqHZjkPWvHAS6ri2Mkk8xxQ0ITLE8JoeWoFcc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\5mmd3bpa8m-gye83jo8yx.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k64ed53csw", "Integrity": "VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12081, "LastWriteTime": "2025-05-20T02:21:15.8335965+00:00"}, "qwzJ90aG/8FIHCZONN3pbLziGPFIVMD7KCx/kyQgYpk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\bb6mdzu3rr-r4e9w2rdcm.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-20T02:21:15.8503589+00:00"}, "QHbSPhyoNbQU3erQiEAcM2mJod9967MmQE6a30qAKrA=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\87p3p3sxch-wl58j5mj3v.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nv0k9gv8u2", "Integrity": "cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11067, "LastWriteTime": "2025-05-20T02:21:15.8641827+00:00"}, "TCfsOgPdAJ+ZSvVSpUw/856c/TmRCIcCx6XeSlJOxZw=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\8557e1bt1t-c2oey78nd0.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-20T02:21:15.8780664+00:00"}, "HlKeHVUpiAUEzpE53YWodH/rWMCk2cFl7xHoXh6vWSk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\gcsiponkd0-d4r6k3f320.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42ih0tfzwc", "Integrity": "rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12033, "LastWriteTime": "2025-05-20T02:21:15.8860609+00:00"}, "quKrq8wKpVwoB5r4uqT8/eXDutYkUsTN4CzH9tpEACs=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\rdldr6dvrr-j5mq2jizvt.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-20T02:21:15.8995579+00:00"}, "mDZrfjhba9WhUxs1yd3JHASZRkVauycsOeyW/UKKekk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\m62uo045e1-keugtjm085.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q8bg8bu1pt", "Integrity": "Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11050, "LastWriteTime": "2025-05-20T02:21:15.9086519+00:00"}, "jMwWW96WR4hIrX1MIWCi5z1HC2ixwB2SRmi3mkaLxw0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\e6bf8zzr7q-nvvlpmu67g.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-20T02:21:15.8356657+00:00"}, "U7o1RWZ4pYuJGyNqJq7jAEyLJXwT1k5ouMvjAaa8N64=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\vt3l1otlwk-zub09dkrxp.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nqh38a62u8", "Integrity": "j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33518, "LastWriteTime": "2025-05-20T02:21:15.8527112+00:00"}, "mc2V2b19QRyi6jyiS0mEVIDtSsaGH4Fs6fA8P2cajtI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\mvesyidxpe-pj5nd1wqec.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-20T02:21:15.8850585+00:00"}, "dEJ4YIP8kLJTlGFgmOd3urN8cJA35Pta4/BiraxCm5Y=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\7hmrxov90n-43atpzeawx.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cmcmkw9yu7", "Integrity": "ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30968, "LastWriteTime": "2025-05-20T02:21:15.8954088+00:00"}, "b5O1ya6gxSPMgIcXLJxclVEcLmwCxZ6E2LtUdNSeDWk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ghm4vcb58f-v0zj4ognzu.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-20T02:21:15.9182404+00:00"}, "rrAnPCRCTwNjfkaLj0usULzq3WFeS9R+BYPeerhUwCM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\zdrjl9ld2m-ynyaa8k90p.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0uw774o5yt", "Integrity": "CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33374, "LastWriteTime": "2025-05-20T02:21:15.9306245+00:00"}, "grWX+mra8bskmw5kwt3z5d+fkGW9ocYT6WKujusoQ0U=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\m4ww1492db-hrwsygsryq.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-20T02:21:15.9616409+00:00"}, "IwQUUbq+iZNNbPXw53duxalreTCXpXNTN5+mX5o8VXo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\eq4mcjdl57-c63t5i9ira.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n2nwmiwapx", "Integrity": "zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30991, "LastWriteTime": "2025-05-20T02:21:15.8399165+00:00"}, "wN71+r6LYQe+2EmSHX/GY+KqE8QNBJXmiXDPOwLeoYQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\3axck6a8j5-ft3s53vfgj.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-20T02:21:15.8503589+00:00"}, "ytKUcJ9tz9k4DuCSOEqJQQQVo5AYgiyViZInL6nPpps=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\pe2oh4g1tt-4094rpi4f9.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=4094rpi4f9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u3mr700haq", "Integrity": "qk/LGv3UCKBZhVKSwN0Lvyprj8TiWmp+iiWhP0Dwdt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44640, "LastWriteTime": "2025-05-20T02:21:15.8699043+00:00"}, "nE+XhHwbbmSzInfh+ghK4oXn4Zrb7kYVLwqCiP8VegA=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\j6y0fr7ovb-6pdc2jztkx.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-20T02:21:15.8965458+00:00"}, "+HZrfOPYiVkSWPKZapMo61dKfst10AgdRwf/2AIO448=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\0pdfclquij-hd3gran6i8.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=hd3gran6i8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yc8010sx1o", "Integrity": "rjTJ9Ut8h6OLoNYsNgr7e/TIritk83K5bucjnu0BPlM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23992, "LastWriteTime": "2025-05-20T02:21:15.9086519+00:00"}, "xYVi6melf9k4kvNR9775wY2V2STXjnRjWrXOje5hFyA=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\n865g5lyle-iovd86k7lj.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-20T02:21:15.9296244+00:00"}, "2d8y2qG4ifEwdLYKDtYG1rb5ZS5e1WqxlnEDCjRPi2Q=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\8ni1fpoqft-ltid2c489k.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=ltid2c489k}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jgdip1sczm", "Integrity": "ozEUTWBbc3wKZYb3ecLdk+9yMj3kbTTBJx6aTKh2FDs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 29043, "LastWriteTime": "2025-05-20T02:21:15.9440454+00:00"}, "8CYjuYNlgBkQ9FgkT29ZYIpOzk9Nc3BWyznodcoSORM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\9ys240qqyp-kbrnm935zg.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-20T02:21:15.8611801+00:00"}, "OWAAQYJo5NUrgowY2lzV+JWdc9E2H9iFs01rRu6WyqA=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\26ewc2xcz3-8vyfqsqgz1.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=8vyfqsqgz1}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0v96h8eab3", "Integrity": "bKh1MfaBAldvWxNRIEd7ZCFzOhTySMaAYostY/K7XbA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18643, "LastWriteTime": "2025-05-20T02:21:15.8591812+00:00"}, "jBIq2Cd7PrK/wsYIhZ6bMcq4fN/hX65IgpYdluFgj2s=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\bgkxf87w2y-y7v9cxd14o.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-20T02:21:15.8601824+00:00"}, "h7A6QcJKS7BJ5MqDJMVeP4dj+Jm31wrDM8y3N5WyNzU=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\prlxa1mn9c-u9q1upor1n.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=u9q1upor1n}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f711a7kh87", "Integrity": "pf2AOjAOccWBEl9ORfBaaHo0t3lQUfisiqfIeMW9tqI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29774, "LastWriteTime": "2025-05-20T02:21:15.8439192+00:00"}, "dJ1vXs2SnK01OsP1pJMvRPX08ZBi7Onp7o/btFsPT2Q=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ic9z2xe8wm-h1s4sie4z3.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-20T02:21:15.8471462+00:00"}, "c2cWmojav/bn/xX3MDj6obiPC6OYrwvkH4MjjbFmzH4=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\zu9mc6302m-4d85u1mtcx.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=4d85u1mtcx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "47nw1ac886", "Integrity": "cAYCo2Q8Ith/zqZVNFuI/cRoMoGlmld9t1DuyUGILPk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16642, "LastWriteTime": "2025-05-20T02:21:15.8376684+00:00"}, "AZfmiQwSygRWs3viREHWzPbDKxiQOhaPZyYjPR06UGE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\0gc5vw8on7-0j3bgjxly4.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-20T02:21:15.8601824+00:00"}, "Jrb3yLpcaJ+/FjP605dYUXwk4+TiYuZaLk9PR8JkvOM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\51hoygrppj-03hjc8e09r.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=03hjc8e09r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj6zx16vtq", "Integrity": "I28FOsKinIPvKQJYJhPmeb5aWTZaShTculcMW3WSRdU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4679, "LastWriteTime": "2025-05-20T02:21:15.8366657+00:00"}, "OyGiSz/4R+kxt6+NoFzJPS/mLJZiMdX9LYz1Te7g8ZM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\jo8len0gxq-48y08845bh.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=48y08845bh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5hgi50qbud", "Integrity": "F1eM1vgG9PaT7pF5Hubo7swbaVAWhXsjYwxoJvyk5+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2206, "LastWriteTime": "2025-05-20T02:21:15.8483421+00:00"}, "JRzLmmK6RBB5UPMTGXaEFdndIEprmSffhu+LfDexehE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\cgv92yu6ou-356vix0kms.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-20T02:21:15.8376684+00:00"}, "JdmSvN1AsU8RYW7QHIXwp5uBjfSl+X6NTtlTN7CiRvQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\649tqy7snk-83jwlth58m.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-20T02:21:15.840925+00:00"}, "Lgl6Mg255yKsQ3WLXWLFkSO6hUO3BLTUojGJE0DIxlg=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\mwr1g6hbqe-mrlpezrjn3.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-20T02:21:15.8567286+00:00"}, "S+Dddc/vAamnyvB2mLnHSzM5/lMcSAXD2nFCPu34EGs=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\pqfds0um0z-worgj0nhwm.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=worgj0nhwm}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xj2ze9hdow", "Integrity": "rdkQBPBwlP4JzOj6r9lyBjOEX/yZn2usvoAwy9AWc1Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14178, "LastWriteTime": "2025-05-20T02:21:15.8709025+00:00"}, "tgZzSLflQ6UWCxAhQOHF+8NoW6xu5deINzIZrabpxeQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\6f661r2pt3-gk0pw8i4co.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=gk0pw8i4co}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q07w49iitf", "Integrity": "itOFoWDZSqZIyZRKcH5nH/mbWKG8zHsAz7G0W9zjagg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8126, "LastWriteTime": "2025-05-20T02:21:15.8790618+00:00"}, "nVgVF6HxQdE+/blrbviGdM0QjAo76KCp1JwWLEJWVrs=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ja0ylivr9u-x0q3zqp4vz.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-20T02:21:15.8591812+00:00"}, "BgmFdvOxN7MhTlYq6bSmcbvtt5qKJXT3aopS7cNrq1c=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\hcayxzl1cn-d6nrnj6jjx.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=d6nrnj6jjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2k5ha5qn83", "Integrity": "nuuauMZg92Lq1w9JnCHRfNGUNc9WeRQGkGwvzcHEyTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 85369, "LastWriteTime": "2025-05-20T02:21:15.8527112+00:00"}, "Da165OcVHHgTaDQMKgSMLQeGw2glsZx833qVxNEV/Qo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\j370ecpawe-m9qm4tazc0.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=m9qm4tazc0}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1s03qn3l9f", "Integrity": "T1Mxz6b5tcPGdGYeBLTL5A9PUSvbDbkClvy0G7J1r2k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30691, "LastWriteTime": "2025-05-20T02:21:15.8677636+00:00"}, "fGNPXiTfUw4iFUb9osTg3j+HBwUHcJSrK6qCeodXO+E=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\pp1rb8slue-ttgo8qnofa.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-20T02:21:15.8581672+00:00"}, "mJN2kXXamyCI+QmyYPTlqqf0EVc7j5kR6HVSNt7FiEo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\9j1x9u35hj-jjsuc0puko.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=jjsuc0puko}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m8w5b0wq9e", "Integrity": "imJ8RM7oNfakmMXAji+mGEXDQYSPHKMWoVjRycEkdZ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 69352, "LastWriteTime": "2025-05-20T02:21:15.8601824+00:00"}, "LSopD/yPfRWodo2cPJnWVpYhVHtLdLEWeJR/Hvs2vBc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\m2m61wosui-9vr69e8ynj.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=9vr69e8ynj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mulfwnbtpa", "Integrity": "oheIXejBlri6EJgnT3rCEOV8cSc0XnYutP+KQ6pSvDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24363, "LastWriteTime": "2025-05-20T02:21:15.8739092+00:00"}, "DaOIMR69QA6VRoQudT6pN3vXEa+c83ikqqdNYo56mbo=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\8c6j3n321y-87fc7y1x7t.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-20T02:21:15.8631882+00:00"}, "ZgmLbP0QmQ6KHnCqJTGx10v6x+av5TTz60H6+cI+c88=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\szl4c0mnrg-mlv21k5csn.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-20T02:21:15.8439192+00:00"}, "28L7wmX/XJwGuUa3PPc2C23oi2fViLpEYx4ZxpkVjYM=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\zr6apqqqgn-w9c3e4a832.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "lib/pannellum/pannellum#[.{fingerprint=w9c3e4a832}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\lib\\pannellum\\pannellum.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e8toqgsfaf", "Integrity": "AjzI/bmR/8/DIkpadLPsGlO87upxDp6N52K7sa0YRbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\lib\\pannellum\\pannellum.css", "FileLength": 1652, "LastWriteTime": "2025-05-20T02:21:15.8621825+00:00"}, "54rWRA3FO5foEjm9C1dQgB2Q7N1zdWWOKIdUdP9zDhY=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\trvpi1geeq-tdadp1kl73.gz", "SourceId": "ViVu", "SourceType": "Computed", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "ViVu#[.{fingerprint=tdadp1kl73}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ViVu.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aws3gmk171", "Integrity": "9qKCLVh9iku2IJX+IrCVDN4aO3ssRD7J9LuPsMhX1PI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ViVu.styles.css", "FileLength": 535, "LastWriteTime": "2025-05-20T02:21:15.8718984+00:00"}, "bodQ2X5qaqUhOScklExU1jLXcO6Vmi6sQcsPd6wdvmk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\5xefkxfry4-tdadp1kl73.gz", "SourceId": "ViVu", "SourceType": "Computed", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "ViVu#[.{fingerprint=tdadp1kl73}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ViVu.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aws3gmk171", "Integrity": "9qKCLVh9iku2IJX+IrCVDN4aO3ssRD7J9LuPsMhX1PI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ViVu.bundle.scp.css", "FileLength": 535, "LastWriteTime": "2025-05-20T02:21:15.8611801+00:00"}, "TssZW1xHVsSZAvS9lSxE/ITXlLdqMg5XAu6EB0jGfSI=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ljccir5lm4-ht3azws4r6.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/site#[.{fingerprint=ht3azws4r6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d13cvtdzdg", "Integrity": "YT3r3KpXswFAP5xx3obt11vaGOLHQPIqOAkiWAwdK/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\site.css", "FileLength": 3029, "LastWriteTime": "2025-05-24T15:26:08.4013413+00:00"}, "FQgJcJkquoJ0EzSYT0XrcYwFq14T7FpUIZqejHSHqaQ=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\k26b0abg92-qxf3li74m7.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "images/destinations/placeholder#[.{fingerprint=qxf3li74m7}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\images\\destinations\\placeholder.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6z4w9<PERSON><PERSON>", "Integrity": "MvYU/iNq5eEU4IHgLS/7B3AS/4cYXIf2XHyJ+Hrm2Mc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\images\\destinations\\placeholder.txt", "FileLength": 288, "LastWriteTime": "2025-05-20T02:38:41.9088612+00:00"}, "GkAkWQLxAgBCtH0FpqzGsjVGVQIe4tdzoNj9Nyf3Qf8=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\0xt2o26pim-em21gwj8yf.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "images/destinations/README#[.{fingerprint=em21gwj8yf}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\images\\destinations\\README.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8td9i7rgyo", "Integrity": "LCwr/MJ/7bILymnphnumDWtCBtlr1Jn29nAAz3d9NWU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\images\\destinations\\README.md", "FileLength": 917, "LastWriteTime": "2025-05-20T02:38:41.9108594+00:00"}, "Mr5OEnAbsXvpDhuCfcVB4aTNxZOMEcv+Ry78FY5pnV0=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\w5xrbqwrzs-d7fsqn8hib.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "images/logo-small#[.{fingerprint=d7fsqn8hib}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\images\\logo-small.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxifr0pvh3", "Integrity": "mljexTLcf0En5AHkwDODgFrsMV1G3eiV2WhJWyJOvsw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\images\\logo-small.txt", "FileLength": 209, "LastWriteTime": "2025-05-20T02:38:41.9128593+00:00"}, "Sy3RV+hw7C1UpCcPK44QebGi4Ab1ZDm+8Jg7K42C840=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\v6xdpi2rsy-v5ss407etk.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/sidebar-nav#[.{fingerprint=v5ss407etk}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-nav.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6a9urljveg", "Integrity": "fukOjtObJ5Mi+hiG1QKPhHh5GvKFmJxeZsZmj1V+GmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-nav.js", "FileLength": 1056, "LastWriteTime": "2025-05-20T03:57:59.5866399+00:00"}, "+J7qTBmnBhgt70nsbwmgYSBeUwElIiJgzWrelNqB0x4=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\cg3wiraxro-vwklwv25qu.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/style#[.{fingerprint=vwklwv25qu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2w7p3ur8bd", "Integrity": "zkAqnFCUqrLvokCWG93B5lsuFah5x29iQ6QPGr0WSQY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\style.css", "FileLength": 6258, "LastWriteTime": "2025-05-24T15:26:08.4038525+00:00"}, "aGPURQBWU6d1cXh0KdXJvX0lRnZecx1vumEAo94DM0k=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\ramy9llpr3-7e5vqfllgd.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/destination-showcase#[.{fingerprint=7e5vqfllgd}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\destination-showcase.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dcggidrchp", "Integrity": "vQyaXXNXvExl/g3zvnCwXi1QSWRVo5nZNkJyfzjdic8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\destination-showcase.js", "FileLength": 3315, "LastWriteTime": "2025-05-24T15:12:11.4161946+00:00"}, "17gyk89Pwi0NZIzqDZIBPUlpV5s+oE0v3gK3PRtdPho=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\w4x2ykis6q-6puonatsi4.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/sidebar-dropdown#[.{fingerprint=6puonatsi4}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-dropdown.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3bv3quyhco", "Integrity": "P7MKWsheqZEKr2bcTfpIcgC+UZ9ujM+RvHClsf3zpU0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-dropdown.js", "FileLength": 1731, "LastWriteTime": "2025-05-24T13:16:09.9050102+00:00"}, "ZVNdwkyS14PcH8++ehAn8vlVorJRlyIMu3U5bCaJRRA=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\oqwpqvi6k9-yv2zrun0z7.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/modern-header#[.{fingerprint=yv2zrun0z7}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\modern-header.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mozoxcbvx4", "Integrity": "WlRwsnivHYD4rfrbZamlXYaXibempI13OmbwrqBV4Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\modern-header.css", "FileLength": 1251, "LastWriteTime": "2025-05-24T12:55:55.2525883+00:00"}, "2VZWVcZX6h3gKr8ltkh8XQQcwsD1t+pqsAZlQMfGKfE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\6ig365r0rr-vjbmj0jut9.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/sidebar-expandable#[.{fingerprint=vjbmj0jut9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-expandable.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rbkdrv377p", "Integrity": "2ZCsyS5wRRXSU/HwrwejubPsbE/GArVWBeY3ssEtYHI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-expandable.js", "FileLength": 1898, "LastWriteTime": "2025-05-24T13:30:01.3763781+00:00"}, "ceDKxZV2Z8SaYNj5DEoDOEdWV6N+ICPueEqusqjzEJk=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\8f2bnajrqc-appegp71ny.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/sidebar-test#[.{fingerprint=appegp71ny}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-test.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pbnwea8ih4", "Integrity": "64tNsWN+R0iQZVkla7YGv8/9IHRVOnLyW/W99eyL6rE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-test.js", "FileLength": 718, "LastWriteTime": "2025-05-24T13:13:51.3429533+00:00"}, "waOcYIizswhj4biDamkgRhpprwbahNOeeYTHBm0KPKE=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\saardgppct-gaksvrxzwe.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/sidebar-nav#[.{fingerprint=gaksvrxzwe}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\sidebar-nav.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xjti02oq76", "Integrity": "Ha7NviUak3B/SvosWam3ZDoe8z4XxbWpAh5E61ZYETQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\sidebar-nav.css", "FileLength": 2541, "LastWriteTime": "2025-05-24T14:23:00.2036294+00:00"}, "qUiBdpYN+qsXqinmwoIKAAmmP4z6rzGNa00AKtTJu/A=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\a7323ou3qk-zocs2a5q8j.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "css/sidebar-expandable#[.{fingerprint=zocs2a5q8j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\css\\sidebar-expandable.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wb6tv398c6", "Integrity": "tD+TWWKHkCvD07TJyvM+t3xyb444q360HV2nPkqvuRc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\css\\sidebar-expandable.css", "FileLength": 3068, "LastWriteTime": "2025-05-24T14:01:56.919162+00:00"}, "oGL4vOCi4TWwhV3BJGQm49ZpiNkmHoQHQFsCOoXrG1s=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\gfhh1aztgj-ds9gxm4yku.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/sidebar-simple-test#[.{fingerprint=ds9gxm4yku}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-simple-test.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ggkb1ujwu", "Integrity": "uEDSkAxNi+bBZtPMLXdo1uwfURMomqk+3lUJ3iwAXNs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\sidebar-simple-test.js", "FileLength": 841, "LastWriteTime": "2025-05-24T13:30:01.382913+00:00"}, "fXqrrr9LoiFqBMqikKqDbUC75QBBCiwFAH2aY3w1aYc=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\faher7y2xb-673tu0o91e.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "test-sidebar#[.{fingerprint=673tu0o91e}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\test-sidebar.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q9lmf8lhgi", "Integrity": "0svTH//4dfqs7T3+H138tjKaYg/qYZwXQymWZrzclus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\test-sidebar.html", "FileLength": 1145, "LastWriteTime": "2025-05-24T13:30:01.3874409+00:00"}, "YNVN7isCqSPOIPxIi50VXQe+5SwQzbN5novEq4BR7o4=": {"Identity": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\3g3j2zgo5t-i89hsaqvex.gz", "SourceId": "ViVu", "SourceType": "Discovered", "ContentRoot": "D:\\project\\ViVu\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ViVu", "RelativePath": "js/homepage-tabs#[.{fingerprint=i89hsaqvex}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\project\\ViVu\\wwwroot\\js\\homepage-tabs.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ljzhzo716v", "Integrity": "zx65g3dci+L5blEu0mKlSnK85aeEC8gltzH0VH6+F3w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\ViVu\\wwwroot\\js\\homepage-tabs.js", "FileLength": 1622, "LastWriteTime": "2025-05-24T14:42:31.4368853+00:00"}}, "CachedCopyCandidates": {}}