/* ✨ Enhanced Expandable Sidebar Styles ✨ */

/* Expandable button styles with enhanced animations */
.sidebar-expandable {
  position: relative;
  margin: 2px 0;
}

.expandable-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 16px;
}

/* Enhanced expand icon with smooth animations */
.expand-icon {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  opacity: 0.6;
  color: inherit;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expandable-toggle.active .expand-icon {
  transform: translateY(-50%) rotate(90deg) scale(1.1);
  opacity: 1;
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.expandable-toggle:hover .expand-icon {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
  background: rgba(255, 255, 255, 0.25);
}

/* Enhanced hover effects for expandable buttons */
.expandable-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
}

.expandable-toggle:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

/* ✨ Enhanced Expanded Sidebar Container ✨ */
.sidebar-expanded {
  position: fixed;
  left: 80px;
  top: 0;
  bottom: 0;
  width: 340px;
  /* ✨ Maximum Transparency with Clear Content ✨ */
  /* ✨ COMPLETELY TRANSPARENT SIDEBAR ✨ */
  background: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;

  /* No borders */
  border: none;

  /* No shadows */
  box-shadow: none;
  z-index: 999;
  transform: translateX(-100%);
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;

  /* No decorative elements */
  /* Ensure it's hidden by default */
  display: none;
}

@keyframes shimmer {
  0%,
  100% {
    opacity: 1;
    transform: translateX(0);
  }
  50% {
    opacity: 0.7;
    transform: translateX(2px);
  }
}

.sidebar-expanded.show {
  transform: translateX(0);
  /* No shadows - completely transparent */
  box-shadow: none;
  /* Show when active */
  display: block;
}

/* ✨ Enhanced Sidebar Content ✨ */
.sidebar-expanded-content {
  height: 100%;
  overflow-y: auto;
  padding: 25px 0;
  position: relative;
}

/* Enhanced expanded menu styles */
.expanded-menu {
  display: none;
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.expanded-menu.active {
  display: block;
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* ✨ FLOATING HEADER CARD ✨ */
.expanded-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  margin: 20px 15px 25px 15px;
  position: relative;

  /* Floating card background */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(20px) saturate(1.5);

  /* Strong floating shadow */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* ✨ High Contrast Header Title ✨ */
.expanded-menu-header h5 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 800;
  /* High contrast solid color instead of gradient */
  color: #1a202c;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.9),
    0 0 12px rgba(76, 175, 80, 0.3), 0 1px 0 rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  letter-spacing: -0.02em;
}

/* ✨ FLOATING CLOSE BUTTON ✨ */
.btn-close-expanded {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px) saturate(1.3);
  padding: 0;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  /* Floating button shadow */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(220, 53, 69, 0.1) 0%,
      rgba(220, 53, 69, 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }
}

.btn-close-expanded:hover {
  background: linear-gradient(
    135deg,
    rgba(220, 53, 69, 0.1) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  color: #dc3545;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);

  &::before {
    opacity: 1;
  }
}

.btn-close-expanded:active {
  transform: scale(0.95) rotate(90deg);
  transition: all 0.1s ease;
}

/* ✨ Enhanced Menu Body ✨ */
.expanded-menu-body {
  padding: 0 15px;
}

/* ✨ Beautiful Expanded Menu Items ✨ */
.expanded-menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin: 8px 0;
  border-radius: 16px;
  text-decoration: none;
  /* ✨ High Contrast Text for Transparency ✨ */
  color: #1a202c;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  /* ✨ Maximum Transparency Menu Items ✨ */
  /* ✨ PROMINENT FLOATING BUTTONS ✨ */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(240, 245, 251, 0.95) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px) saturate(1.5);

  /* Strong shadows for floating effect */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);

  /* Hover effect preparation */
}

.expanded-menu-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(76, 175, 80, 0.1) 50%,
    transparent 100%
  );
  transition: left 0.5s ease;
}

.expanded-menu-item:hover {
  /* ✨ Ultra Transparent Hover with High Contrast Text ✨ */
  /* ✨ ENHANCED FLOATING BUTTON HOVER ✨ */
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.1) 0%,
    rgba(255, 255, 255, 0.98) 30%,
    rgba(255, 255, 255, 0.95) 70%,
    rgba(76, 175, 80, 0.08) 100%
  );
  color: #2d5a2f;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.9),
    0 0 8px rgba(76, 175, 80, 0.3);
  text-decoration: none;
  transform: translateX(8px) translateY(-4px);
  /* Enhanced floating effect */
  box-shadow: 0 12px 48px rgba(76, 175, 80, 0.2), 0 8px 32px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 1);
  border-color: rgba(76, 175, 80, 0.4);
  backdrop-filter: blur(25px) saturate(1.8);
}

.expanded-menu-item:hover::before {
  left: 100%;
}

.expanded-menu-item:active {
  transform: translateX(6px) translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

/* ✨ High Contrast Icons for Transparency ✨ */
.expanded-menu-item i {
  font-size: 20px;
  width: 28px;
  text-align: center;
  flex-shrink: 0;
  margin-right: 4px;
  transition: all 0.3s ease;
  /* Ultra transparent icon background */
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.03) 0%,
    rgba(76, 175, 80, 0.01) 100%
  );
  border-radius: 8px;
  padding: 6px;
  /* High contrast icon color */
  color: #2d5a2f;
  font-weight: 900;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8),
    0 0 6px rgba(76, 175, 80, 0.4);
}

.expanded-menu-item:hover i {
  transform: scale(1.15) rotate(8deg);
  /* Ultra transparent hover background */
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.05) 0%,
    rgba(76, 175, 80, 0.02) 100%
  );
  /* Enhanced glow effect */
  color: #1e4a20;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 1), 0 0 12px rgba(76, 175, 80, 0.6),
    0 0 20px rgba(76, 175, 80, 0.3);
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.1),
    0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* ✨ High Contrast Title Text ✨ */
.expanded-menu-item .item-title {
  font-weight: 700;
  font-size: 1rem;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
  letter-spacing: -0.01em;
  transition: all 0.3s ease;
  /* Enhanced text contrast */
  color: inherit;
  text-shadow: inherit;
}

.expanded-menu-item:hover .item-title {
  font-weight: 800;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 1), 0 0 8px rgba(76, 175, 80, 0.4);
}

/* ✨ High Contrast Description Text ✨ */
.expanded-menu-item .item-desc {
  font-size: 0.85rem;
  color: #4a5568;
  line-height: 1.3;
  transition: all 0.3s ease;
  opacity: 0.9;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
}

.expanded-menu-item:hover .item-desc {
  color: #2d5a2f;
  opacity: 1;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9),
    0 0 6px rgba(76, 175, 80, 0.2);
}

/* ✨ Enhanced Danger Items ✨ */
.expanded-menu-item.text-danger {
  color: #dc3545;
  border-color: rgba(220, 53, 69, 0.1);
}

.expanded-menu-item.text-danger i {
  background: linear-gradient(
    135deg,
    rgba(220, 53, 69, 0.1) 0%,
    rgba(220, 53, 69, 0.05) 100%
  );
  color: #dc3545;
}

.expanded-menu-item.text-danger:hover {
  background: linear-gradient(
    135deg,
    rgba(220, 53, 69, 0.08) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(220, 53, 69, 0.05) 100%
  );
  color: #dc3545;
  border-color: rgba(220, 53, 69, 0.2);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.6),
    0 8px 25px rgba(220, 53, 69, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
}

.expanded-menu-item.text-danger:hover i {
  background: linear-gradient(
    135deg,
    rgba(220, 53, 69, 0.2) 0%,
    rgba(220, 53, 69, 0.1) 100%
  );
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

.expanded-menu-item.text-danger:hover .item-desc {
  color: #dc3545;
}

/* ✨ Beautiful Menu Divider ✨ */
.expanded-menu-divider {
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(76, 175, 80, 0.2) 20%,
    rgba(76, 175, 80, 0.4) 50%,
    rgba(76, 175, 80, 0.2) 80%,
    transparent 100%
  );
  margin: 20px 20px;
  border-radius: 1px;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.8) 50%,
      transparent 100%
    );
  }
}

/* ✨ Enhanced Badge Styling ✨ */
.expanded-menu-item .badge {
  font-size: 0.7rem;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  letter-spacing: 0.02em;
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* ✨ Enhanced Content Wrapper ✨ */
.wrapper.sidebar-expanded-active {
  /* Keep normal sidebar spacing - no additional margin */
  margin-left: 80px;
  width: calc(100% - 80px);
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* ✨ Enhanced Responsive Styles ✨ */
@media (max-width: 1199px) {
  .sidebar-expanded {
    width: 300px;
  }

  .wrapper.sidebar-expanded-active {
    /* Keep normal sidebar width on smaller screens */
    margin-left: 80px;
    width: calc(100% - 80px);
  }
}

@media (max-width: 991px) {
  .sidebar-expanded {
    left: 0;
    width: 100vw;
    z-index: 1060;
    /* ✨ COMPLETELY TRANSPARENT MOBILE ✨ */
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }

  .wrapper.sidebar-expanded-active {
    margin-left: 0;
    width: 100%;
  }

  .expanded-menu-item {
    padding: 18px 25px;
    margin: 10px 0;
  }

  .expanded-menu-header {
    padding: 0 30px 25px 30px;
  }
}

/* Overlay for mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Animation for menu items */
.expanded-menu.active .expanded-menu-item {
  animation: slideInUp 0.3s ease forwards;
}

.expanded-menu.active .expanded-menu-item:nth-child(1) {
  animation-delay: 0.1s;
}
.expanded-menu.active .expanded-menu-item:nth-child(2) {
  animation-delay: 0.15s;
}
.expanded-menu.active .expanded-menu-item:nth-child(3) {
  animation-delay: 0.2s;
}
.expanded-menu.active .expanded-menu-item:nth-child(4) {
  animation-delay: 0.25s;
}
.expanded-menu.active .expanded-menu-item:nth-child(5) {
  animation-delay: 0.3s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ✨ Beautiful Scrollbar Styling ✨ */
.sidebar-expanded-content::-webkit-scrollbar {
  width: 8px;
}

.sidebar-expanded-content::-webkit-scrollbar-track {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(248, 250, 252, 0.1) 100%
  );
  border-radius: 4px;
}

.sidebar-expanded-content::-webkit-scrollbar-thumb {
  background: linear-gradient(
    180deg,
    rgba(76, 175, 80, 0.3) 0%,
    rgba(76, 175, 80, 0.5) 50%,
    rgba(76, 175, 80, 0.3) 100%
  );
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.sidebar-expanded-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    180deg,
    rgba(76, 175, 80, 0.5) 0%,
    rgba(76, 175, 80, 0.7) 50%,
    rgba(76, 175, 80, 0.5) 100%
  );
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

/* ✨ Enhanced Animations ✨ */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Staggered animation delays for menu items */
.expanded-menu.active .expanded-menu-item:nth-child(1) {
  animation-delay: 0.1s;
}
.expanded-menu.active .expanded-menu-item:nth-child(2) {
  animation-delay: 0.15s;
}
.expanded-menu.active .expanded-menu-item:nth-child(3) {
  animation-delay: 0.2s;
}
.expanded-menu.active .expanded-menu-item:nth-child(4) {
  animation-delay: 0.25s;
}
.expanded-menu.active .expanded-menu-item:nth-child(5) {
  animation-delay: 0.3s;
}
.expanded-menu.active .expanded-menu-item:nth-child(6) {
  animation-delay: 0.35s;
}
