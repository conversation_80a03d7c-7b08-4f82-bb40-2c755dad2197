html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus,
.btn:active:focus,
.btn-link.nav-link:focus,
.form-control:focus,
.form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

/* Layout adjustments for sidebar */
body.full-page-layout {
  margin-left: 80px; /* Space for sidebar */
  transition: margin-left 0.3s ease;
}

/* Ensure main content doesn't overlap with sidebar */
.main-content {
  margin-left: 0; /* Reset since body already has margin */
  width: calc(100% - 0px); /* Full width minus any additional margins */
}

/* Markdown content styling */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 1.8rem;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3rem;
}

.markdown-content h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3rem;
}

.markdown-content h3 {
  font-size: 1.3rem;
}

.markdown-content h4 {
  font-size: 1.1rem;
}

.markdown-content p {
  margin-bottom: 1rem;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.markdown-content li {
  margin-bottom: 0.5rem;
}

.markdown-content blockquote {
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid #dfe2e5;
  color: #6a737d;
}

.markdown-content code {
  padding: 0.2rem 0.4rem;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
  font-size: 85%;
}

.markdown-content pre {
  padding: 1rem;
  overflow: auto;
  background-color: #f6f8fa;
  border-radius: 3px;
  margin-bottom: 1rem;
}

.markdown-content pre code {
  padding: 0;
  background-color: transparent;
}

.markdown-content table {
  width: 100%;
  margin-bottom: 1rem;
  border-collapse: collapse;
}

.markdown-content table th,
.markdown-content table td {
  padding: 0.5rem;
  border: 1px solid #dfe2e5;
}

.markdown-content table th {
  background-color: #f6f8fa;
  font-weight: 600;
}

/* AI recommendation styling */
.ai-content {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border-left: 4px solid #0d6efd;
}

.form-floating > .form-control-plaintext::placeholder,
.form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder,
.form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* ===== HOMEPAGE DESTINATION SHOWCASE STYLES ===== */

/* Full page container */
.full-page {
  height: 100vh;
  overflow: hidden;
  width: 100%; /* Ensure full width within the margin */
}

/* Destination Showcase */
.destination-showcase {
  position: relative;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.destination-showcase.asia {
  background-image: url("/images/ben-tre-bg.jpg");
}

/* Destination Container */
.destination-container {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}

/* Showcase Content Layout */
.showcase-content {
  display: flex;
  height: calc(100vh - 80px); /* Leave space for bottom controls */
  flex: 1;
}

/* Left side - Continent Info */
.showcase-info {
  flex: 0 0 40%;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 2;
}

.continent-name {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.continent-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.explore-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  align-self: flex-start;
}

.explore-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Right side - Destinations with tabs */
.showcase-destinations {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: 100%;
  min-height: 500px;
}

/* Tab Navigation */
.showcase-tab-navigation {
  margin-bottom: 20px;
}

.tab-nav-container {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.tab-nav-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.tab-nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  color: white;
  transform: translateY(-2px);
}

.tab-nav-btn.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.tab-nav-btn i {
  font-size: 1rem;
}

/* Tab Content - Lower specificity, will be overridden by destination-showcase.css */
.showcase-tab-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 30px;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

/* These rules are overridden by destination-showcase.css */
.tab-content-panel {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content-panel.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Bottom Controls */
.showcase-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.continent-nav {
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
  font-weight: 500;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.continent-indicators {
  display: flex;
  gap: 10px;
}

.continent-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.continent-indicator.active {
  background: white;
  transform: scale(1.2);
}

/* ===== TAB CONTENT STYLES ===== */

/* Search Form Styles */
.search-form-modern {
  color: white;
}

.search-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-tab-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-tab-btn:hover,
.search-tab-btn.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
}

.search-form-group {
  margin-bottom: 15px;
}

.search-form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.search-form-group input,
.search-form-group select {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(5px);
}

.search-form-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 25px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 10px;
}

.search-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Destination Grid */
.destination-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.destination-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.destination-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.destination-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.destination-card-content {
  padding: 15px;
}

.destination-card h4 {
  color: white;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.destination-card p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.destination-card .price {
  color: #ffd700;
  font-weight: 600;
}

/* Hotel/Tour Cards */
.hotel-grid,
.tour-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.hotel-card,
.tour-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.hotel-card:hover,
.tour-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.hotel-card img,
.tour-card img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.hotel-card-content,
.tour-card-content {
  padding: 20px;
}

.hotel-card h4,
.tour-card h4 {
  color: white;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.hotel-card p,
.tour-card p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.rating {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;
}

.rating .stars {
  color: #ffd700;
}

.rating .score {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* Experience Cards */
.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.experience-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.experience-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.experience-card h4 {
  color: white;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.experience-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* About Section */
.about-content {
  color: white;
}

.about-content h3 {
  margin-bottom: 15px;
  color: white;
}

.about-content p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 15px;
}

.newsletter-signup {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-top: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-signup h4 {
  color: white;
  margin-bottom: 15px;
}

.newsletter-form {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.newsletter-form input {
  flex: 1;
  min-width: 200px;
  padding: 10px 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(5px);
}

.newsletter-form input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.newsletter-form button {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-form button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  /* Remove sidebar margin on mobile */
  body.full-page-layout {
    margin-left: 0;
  }

  .showcase-content {
    flex-direction: column;
    height: auto;
  }

  .showcase-info {
    flex: none;
    padding: 30px 20px;
    text-align: center;
  }

  .continent-name {
    font-size: 2.5rem;
  }

  .continent-description {
    font-size: 1rem;
  }

  .showcase-destinations {
    flex: none;
    padding: 20px;
  }

  .tab-nav-container {
    justify-content: center;
    gap: 5px;
  }

  .tab-nav-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .tab-nav-btn span {
    display: none;
  }

  .showcase-tab-content {
    padding: 20px;
    min-height: 400px;
    max-height: none;
  }

  .destination-grid,
  .hotel-grid,
  .tour-grid,
  .experience-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .showcase-controls {
    padding: 0 20px;
    height: 60px;
  }

  .continent-nav span {
    font-size: 0.9rem;
  }

  .nav-btn {
    width: 35px;
    height: 35px;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .newsletter-form input {
    min-width: auto;
  }
}
